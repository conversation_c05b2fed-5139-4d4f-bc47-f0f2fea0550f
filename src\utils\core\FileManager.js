const fs = require('fs');
const path = require('path');
const logger = require('../logger');

/**
 * 文件管理类 - 处理目录创建和文件操作
 */
class FileManager {
  constructor(options = {}) {
    this.options = {
      companyName: '宁波国富商业保理有限公司',
      ...options,
    };
  }

  /**
   * 清理文件名，移除不合法字符
   * @param {string} fileName - 原始文件名
   */
  sanitizeFileName(fileName) {
    if (!fileName) return '';
    return fileName.replace(/[<>:"/\\|?*]/g, '_').trim();
  }

  /**
   * 确保目录存在
   * @param {string} dirPath - 目录路径
   */
  ensureDirectory(dirPath) {
    try {
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        logger.info(`📁 目录创建成功: "${dirPath}"`);
        return true;
      } else {
        logger.info(`📁 目录已存在: "${dirPath}"`);
        return false;
      }
    } catch (error) {
      logger.error(`创建目录失败: ${dirPath}`, error);
      throw new Error(`无法创建目录: ${dirPath}`);
    }
  }

  /**
   * 创建完整的目录结构
   * @param {string} archiveDir - 归档根目录
   * @param {string} customerName - 客户名称
   * @param {string} contractNumber - 合同编号
   * @param {string} withdrawalNumber - 提款流程编号
   */
  createDirectoryStructure(
    archiveDir,
    customerName,
    contractNumber,
    withdrawalNumber,
    rongZiNumber,
  ) {
    if (!archiveDir || !customerName) {
      throw new Error('归档目录和客户名称不能为空');
    }

    // 1. 客户目录
    const sanitizedCustomerName = this.sanitizeFileName(customerName);
    const customerDir = path.join(archiveDir, sanitizedCustomerName);
    this.ensureDirectory(customerDir);

    // 2. 公司目录
    const companyDir = path.join(customerDir, this.options.companyName);
    this.ensureDirectory(companyDir);

    // 3.1 放款交易时间
    let finalDir = companyDir;
    if (contractNumber && contractNumber.length >= 7) {
      const shortContractName = contractNumber.substring(0, 7);
      const sanitizedContractName = this.sanitizeFileName(shortContractName);
      finalDir = path.join(companyDir, sanitizedContractName);
      this.ensureDirectory(finalDir);
    }

    // 3.2 贷后文件
    let daiHouFileDir = null;
    const daiHouFileName = this.sanitizeFileName('贷后文件');
    daiHouFileDir = path.join(companyDir, daiHouFileName);
    this.ensureDirectory(daiHouFileDir);

    // 4. 提款文件和转让文件目录
    const withdrawalDir = path.join(finalDir, '提款文件');
    const transferDir = path.join(finalDir, '转让文件');

    this.ensureDirectory(withdrawalDir);
    this.ensureDirectory(transferDir);

    // 5. 提款流程编号目录（如果有）
    let withdrawalNumberDir = withdrawalDir;
    if (withdrawalNumber && withdrawalNumber.trim()) {
      const sanitizedWithdrawalNumber = this.sanitizeFileName(
        withdrawalNumber.trim(),
      );
      withdrawalNumberDir = path.join(withdrawalDir, sanitizedWithdrawalNumber);
      this.ensureDirectory(withdrawalNumberDir);
    }

    // 6. 融资申请号目录（如果有）
    let rongZiNumberDir = transferDir;
    if (rongZiNumber && rongZiNumber.trim()) {
      const sanitizedRongZiNumber = this.sanitizeFileName(rongZiNumber.trim());
      rongZiNumberDir = path.join(transferDir, sanitizedRongZiNumber);
      this.ensureDirectory(rongZiNumberDir);
    }

    // 7 贷后文件子文件夹
    const daoQiRiDir = path.join(daiHouFileDir, '1-账款到期日调整');
    const rongZiDaoQiRiDir = path.join(daiHouFileDir, '2-融资到期日调整');
    const xiFeiBianGengDir = path.join(daiHouFileDir, '3-息费变更');
    const zhanQiDengJiDir = path.join(daiHouFileDir, '4-展期登记');
    const faPiaoKaiPiaoDir = path.join(daiHouFileDir, '5-发票开票信息');
    const fanZhuanRangDir = path.join(daiHouFileDir, '6-反转让');
    const zhangKuanGenZongDir = path.join(daiHouFileDir, '7-帐款跟踪');

    this.ensureDirectory(daoQiRiDir);
    this.ensureDirectory(rongZiDaoQiRiDir);
    this.ensureDirectory(xiFeiBianGengDir);
    this.ensureDirectory(zhanQiDengJiDir);
    this.ensureDirectory(faPiaoKaiPiaoDir);
    this.ensureDirectory(fanZhuanRangDir);
    this.ensureDirectory(zhangKuanGenZongDir);

    // 8 客户基本信息
    const customerBasicInfoDir = path.join(companyDir, '客户基本信息');
    this.ensureDirectory(customerBasicInfoDir);

    // 9. 主合同
    const mainContractDir = path.join(
      companyDir,
      '主合同-框架合同，如保理服务合同、担保合同、票据让与担保合同等',
    );
    this.ensureDirectory(mainContractDir);

    return {
      customerDir,
      companyDir,
      finalDir,
      withdrawalDir,
      transferDir,
      withdrawalNumberDir,
      rongZiNumberDir,
      daiHouFileDir,
      daoQiRiDir,
      rongZiDaoQiRiDir,
      fanZhuanRangDir,
      zhangKuanGenZongDir,
      customerBasicInfoDir,
      mainContractDir,
    };
  }
}

module.exports = FileManager;
