const path = require('path');
const fs = require('fs');
const logger = require('../utils/logger');
const { formatTime } = require('../utils/util');
const SELECTORS = require('../utils/config/selectors');
const { getZiChanDaoQi } = require('./daihou_process');
const { getCustomerBasicInfo } = require('./customer_process');
const { getMainContract } = require('./contract_process');
const { getZhongDengReport } = require('./zhongdeng_process');
const {
  downloadPDFFile,
  clickAndPrint,
  downloadFromURL,
} = require('../utils/pdfDownloader');

/**
 * 提款申请处理器 - 核心业务逻辑
 */
class WithdrawalProcessor {
  constructor(elementOp, navigationManager, fileManager, configInstance) {
    this.elementOp = elementOp;
    this.navigationManager = navigationManager;
    this.fileManager = fileManager;
    this.configInstance = configInstance;
    this.dirs = null;
    this.detailPageUrl = null;
    this.customerName = null;
  }

  /**
   * 处理详情页面
   */
  async processDetailPage() {
    // 等待页面稳定
    await this.elementOp.page.waitForLoadState('networkidle', {
      timeout: 15000,
    });
    await this.elementOp.page.waitForTimeout(5000);

    this.detailPageUrl = this.elementOp.page.url();

    logger.info('提取客户信息和申请号');
    // 提取页面数据
    const customerName = await this.elementOp.getText(
      SELECTORS.detail.customerName,
    );
    this.customerName = customerName;
    const LoanTransactionTime = await this.elementOp.getText(
      SELECTORS.detail.LoanTransactionTime,
    );
    const withdrawalNumber = await this.elementOp.getText(
      SELECTORS.detail.withdrawalNumber,
    );

    // 提取融资申请号
    const rongZiNumber = await this.elementOp.getText(
      SELECTORS.detail.rongZiNumber,
    );

    logger.info('创建归档目录结构');
    // 创建目录结构
    const archiveDir = this.configInstance.get('customSettings.archiveDir');
    if (!archiveDir) {
      logger.warn('配置中未找到archiveDir，跳过目录创建');
      return;
    }

    const dirs = this.fileManager.createDirectoryStructure(
      archiveDir,
      customerName,
      LoanTransactionTime,
      withdrawalNumber,
      rongZiNumber,
    );
    this.dirs = dirs;

    logger.info('下载相关文档');
    // 下载文件
    await this.downloadFinancingApplication(dirs.withdrawalNumberDir);

    await downloadPDFFile(
      SELECTORS.detail.loan,
      this.elementOp.page,
      dirs.withdrawalNumberDir,
      '2-业务付款审批流程',
    );

    await clickAndPrint(
      SELECTORS.detail.print,
      this.elementOp.page,
      dirs.withdrawalNumberDir,
      '3-放款水单', // TODO: 按原名称进行保存
    );

    // 寻找关联合同下的应收帐款转让通知函
    await this.getGuanLianContractInfo(dirs);

    // 获取基础交易合同
    await this.getBasicContractFile(dirs);

    // 中登登记相关文件;
    await getZhongDengReport(this.elementOp.page, this.dirs.rongZiNumberDir);

    // 业务报价审批流程;
    await this.getApprovalProcess(rongZiNumber);

    // 融资申请审批流程
    await this.getFinancingApplicationApprovalProcess(rongZiNumber);

    // 账款到期日调整
    await getZiChanDaoQi(this.elementOp.page, 'dqr', customerName, this.dirs);

    await getZiChanDaoQi(this.elementOp.page, 'fzr', customerName, this.dirs);

    // 帐款跟踪
    await getZiChanDaoQi(this.elementOp.page, 'zkgz', customerName, this.dirs);

    // 获取客户基本信息
    await getCustomerBasicInfo(
      this.elementOp.page,
      this.dirs.customerBasicInfoDir,
      customerName,
      this.detailPageUrl,
    );

    // 主合同-框架合同
    await getMainContract(this.elementOp.page, this.dirs.mainContractDir);

    logger.success('文件下载完成');
  }

  /**
   * 下载融资申请书
   */
  async downloadFinancingApplication(downloadDir) {
    try {
      // 等待表格加载
      const tableBody = this.elementOp.page
        .locator(SELECTORS.detail.financingApplicationLink)
        .locator('xpath=ancestor::tbody');
      await tableBody.waitFor({ state: 'visible', timeout: 10000 });

      // 获取所有行
      const rows = tableBody.locator('tr');
      const rowCount = await rows.count();

      for (let i = 0; i < rowCount; i++) {
        const row = rows.nth(i);

        // 获取第4列的文本内容
        const fourthTd = row.locator('td').nth(3); // 索引从0开始，第4列是索引3
        const cellText = await fourthTd.textContent();

        if (cellText && cellText.includes('融资申请书')) {
          // 查找该行中的下载链接
          const downloadLink = fourthTd.locator('a').first();
          const linkCount = await downloadLink.count();
          const fileName = await downloadLink.textContent();

          // TODO: 获取编号

          if (linkCount > 0) {
            const success = await downloadPDFFile(
              downloadLink,
              this.elementOp.page,
              downloadDir,
              `1-${fileName}`,
            );

            if (!success) {
              logger.warn('融资申请书下载失败');
            } else {
              return true;
            }
          } else {
            logger.warn(`第 ${i + 1} 行找到融资申请书但没有下载链接`);
          }
          break; // 找到第一个匹配的就退出
        }
      }

      logger.warn('未找到包含"融资申请书"的行');
      return false;
    } catch (error) {
      logger.error(`下载融资申请书失败: ${error.message}`, error);
      return false;
    }
  }

  async getFinancingApplicationApprovalProcess(rongZiNumber) {
    logger.info(`开始获取融资申请审批流程: ${rongZiNumber}`);
    try {
      // 参数验证
      if (!rongZiNumber || !rongZiNumber.trim()) {
        throw new Error('融资申请号不能为空');
      }

      if (!this.dirs || !this.dirs.rongZiNumberDir) {
        throw new Error('目录结构未初始化，请先处理详情页面');
      }

      // 定位融资申请号按钮
      const rongZiBtn = this.elementOp.page.locator(
        SELECTORS.detail.rongZiNumber,
      );

      // 等待按钮可见
      await rongZiBtn.waitFor({ state: 'visible', timeout: 10000 });

      // 监听新页面创建
      const newPagePromise = this.elementOp.page.context().waitForEvent('page');

      // 点击融资申请号按钮，会打开一个新页面
      await rongZiBtn.click();

      // 等待新页面打开
      const newPage = await newPagePromise;
      await newPage.waitForLoadState('networkidle', { timeout: 15000 });
      await newPage.waitForTimeout(2000);

      // 在新页面中寻找打印按钮
      const printButton = newPage.getByRole('button', { name: '打印' });
      await printButton.waitFor({ state: 'visible', timeout: 10000 });
      const fileName = await newPage.title();
      logger.info(`打印文件名称：${fileName}`);

      // 调用 clickAndPrint 方法下载PDF
      const success = await clickAndPrint(
        printButton,
        newPage,
        this.dirs.rongZiNumberDir,
        null,
        '6-',
      );

      // 关闭新页面
      await newPage.close();

      return success;
    } catch (error) {
      logger.error(`获取融资申请审批流程失败: ${error.message}`, error);
      return false;
    } finally {
      // 返回到原详情页
      if (this.detailPageUrl) {
        await this.elementOp.page.goto(this.detailPageUrl, {
          timeout: 15000,
          waitUntil: 'domcontentloaded',
        });
        await this.elementOp.page.waitForLoadState('networkidle', {
          timeout: 10000,
        });
        logger.info('已返回到原详情页');
      }
      logger.info('融资申请审批流程处理完成');
    }
  }

  async getApprovalProcess(rongZiNumber) {
    logger.info(`开始获取业务报价审批流程`);
    try {
      // 参数验证
      if (!rongZiNumber || !rongZiNumber.trim()) {
        throw new Error('融资申请号不能为空');
      }

      if (!this.dirs || !this.dirs.rongZiNumberDir) {
        throw new Error('目录结构未初始化，请先处理详情页面');
      }

      // 使用当前页面直接跳转
      const approvalPage = this.elementOp.page;

      const approvalUrl =
        'https://gfconsole-wsuat.syitservice.com/web-tenant/index.html#/operation-web/quotationManage';

      await approvalPage.goto(approvalUrl, {
        timeout: 30000,
        waitUntil: 'domcontentloaded',
      });

      // 等待页面完全加载
      await approvalPage.waitForLoadState('networkidle', { timeout: 15000 });

      // 检查页面是否可见（如果是无头模式，这个检查会跳过）
      if (!(await approvalPage.context().browser().isConnected())) {
        throw new Error('浏览器连接已断开');
      }

      // 等待页面稳定
      await approvalPage.waitForTimeout(3000);

      // 定位融资申请号输入框并填入
      const rongZiInputSelector =
        'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[1]/form/div/div[1]/div[2]/div/div/input';

      await approvalPage.waitForSelector(rongZiInputSelector, {
        timeout: 10000,
      });
      await approvalPage.fill(rongZiInputSelector, rongZiNumber.trim());

      // 点击查询按钮
      const searchButton = approvalPage.getByRole('button', { name: '查询' });
      await searchButton.click();

      // 等待查询结果加载
      await approvalPage.waitForLoadState('networkidle', { timeout: 15000 });
      await approvalPage.waitForTimeout(2000);

      // 获取表格并查找对应的记录
      const tableSelector =
        'xpath=/html/body/div[1]/div/div/div[2]/div/div[2]/div[2]/div[2]/div[5]/div[2]/table/tbody';
      await approvalPage.waitForSelector(tableSelector, { timeout: 10000 });

      // 查找表格中的详情按钮并点击
      const detailButton = approvalPage.getByRole('button', { name: '详情' });
      await detailButton.waitFor({ state: 'visible', timeout: 10000 });
      await detailButton.click();

      // 等待详情页面加载完成
      await approvalPage.waitForLoadState('networkidle', { timeout: 15000 });
      await approvalPage.waitForTimeout(3000);

      // 等待页面内容元素加载
      await approvalPage.waitForSelector('#pageContent', { timeout: 10000 });

      // 生成截图文件路径
      const screenshotPath = path.join(
        this.dirs.rongZiNumberDir,
        `5-业务报价审批流程.png`,
      );

      // 获取#pageContent元素
      const pageContentLocator = approvalPage.locator('#pageContent');

      // 直接对该元素截图
      const buffer = await pageContentLocator.screenshot();

      // 将截图保存为文件
      fs.writeFileSync(screenshotPath, buffer);

      logger.success(`5-业务报价审批流程.png 截图成功`);
      return true;
    } catch (error) {
      logger.error(`业务报价审批流程.png 截图失败: ${error.message}`, error);
      return false;
    } finally {
      // 返回到原详情页
      if (this.detailPageUrl) {
        await this.elementOp.page.goto(this.detailPageUrl, {
          timeout: 15000,
          waitUntil: 'domcontentloaded',
        });
        await this.elementOp.page.waitForLoadState('networkidle', {
          timeout: 10000,
        });
        logger.info('已返回到原详情页');
      }
      logger.info('审批流程处理完成');
    }
  }

  /**
   * 获取基础交易合同
   */
  async getBasicContractFile(dirs) {
    logger.info(`开始获取基础交易合同`);
    try {
      await this.elementOp.click(SELECTORS.detail.ziChanDetailBtn);

      // 等待弹窗加载
      await this.elementOp.page.waitForTimeout(2000);

      await this.elementOp.click(SELECTORS.detail.ziChanFileListBtn);

      // 等待文件列表加载
      await this.elementOp.page.waitForTimeout(3000);
      const contractElement = this.elementOp.page.locator(
        SELECTORS.detail.jiChuJiaoYiContract,
      );

      await contractElement
        .first()
        .waitFor({ state: 'attached', timeout: 10000 });

      const len = await contractElement.count();

      for (let i = 0; i < len; i++) {
        // 先获取当前行的HTML结构
        const currentRow = contractElement.nth(i);

        // 尝试不同的选择器
        const allTds = currentRow.locator('td');
        const tdCount = await allTds.count();

        if (tdCount >= 2) {
          // 获取第二个td的内容
          const secondTd = allTds.nth(1);

          // 尝试获取.cell元素
          const linkElement = secondTd.locator('.cell span.linkTxt');

          const fileName = await linkElement.textContent();
          const success = await downloadPDFFile(
            linkElement,
            this.elementOp.page,
            dirs.rongZiNumberDir,
            `3-${fileName}`,
          );
          if (!success) {
            throw new Error('基础交易合同下载失败');
          }
        }
      }

      // 关闭弹窗（通过ESC键或点击关闭按钮）
      try {
        const closeBtn = this.elementOp.page.getByRole('button', {
          name: 'Close',
        });
        await closeBtn.click();
        await this.elementOp.page.waitForTimeout(500);
      } catch (closeError) {
        logger.warn(`关闭弹窗失败，可能已自动关闭: ${closeError.message}`);
      }

      return true;
    } catch (error) {
      logger.error('获取基础交易合同失败', error);
      throw error;
    }
  }

  /**
   * 获取关联合同信息并打印所有文本
   */
  async getGuanLianContractInfo(dirs) {
    logger.info(`开始获取关联合同信息`);
    try {
      // 等待关联合同表格加载
      const contractTable = this.elementOp.page.locator(
        SELECTORS.detail.guanLianContract,
      );
      await contractTable.waitFor({ state: 'visible', timeout: 10000 });

      // 获取所有行
      const rows = contractTable.locator('tr');
      const rowCount = await rows.count();

      if (rowCount === 0) {
        logger.warn('未找到关联合同数据');
        return;
      }

      // 遍历每一行并检查第二个td的内容
      for (let i = 0; i < rowCount; i++) {
        const row = rows.nth(i);

        // 获取第二个td的文本内容
        const secondTd = row.locator('td').nth(1);
        const secondTdText = await secondTd.textContent();

        // 过滤包含"应收帐款转让通知函"的数据
        if (secondTdText.includes('应收账款转让通知函')) {
          const thirdTd = row.locator('td').nth(2);
          const contractNo = await thirdTd.textContent();

          // 尝试查找该td中的下载链接
          const downloadLink = secondTd.locator('a').first();
          const href = await downloadLink.getAttribute('href');

          const success = await downloadFromURL(
            href,
            this.elementOp.page,
            dirs.rongZiNumberDir,
            '1-应收账款转让通知函-' + contractNo,
          );

          if (!success) {
            logger.warn(`应收账款转让通知函下载失败: ${contractNo}`);
          }
        } else {
          logger.debug(
            `第 ${i + 1} 行第二个td不包含"应收帐款转让通知函": ${secondTdText}`,
          );
        }
      }

      return true;
    } catch (error) {
      logger.error('获取关联合同信息失败', error);
      throw error;
    }
  }

  /**
   * 查询单个提款申请号
   */
  async searchSingleWithdrawal(withdrawalNumber) {
    logger.info(`开始查询提款申请号: ${withdrawalNumber}`);
    // 填写查询条件
    await this.elementOp.fillInput(SELECTORS.search.input, withdrawalNumber);
    await this.elementOp.click(SELECTORS.search.button);
    await this.elementOp.waitForResults();

    // 等待查询结果加载
    await this.elementOp.page.waitForSelector(
      '.el-table__body-wrapper tr.el-table__row',
      { timeout: 15000 },
    );

    await this.elementOp.page.waitForLoadState('networkidle', {
      timeout: 10000,
    });
    await this.elementOp.page.waitForTimeout(2000);

    // 验证查询结果
    const rows = await this.elementOp.page
      .locator('.el-table__body-wrapper tr.el-table__row')
      .all();
    const hasTargetRow = await Promise.all(
      rows.map(async (row) => {
        const text = await row.textContent();
        return text.includes(withdrawalNumber);
      }),
    );

    if (rows.length !== 1 || !hasTargetRow.some(Boolean)) {
      throw new Error(
        `查询结果不匹配，期望找到唯一包含 ${withdrawalNumber} 的记录`,
      );
    }

    logger.info('进入详情页面处理');
    // 点击详情按钮
    await this.elementOp.click(SELECTORS.search.detailButton);
    await this.elementOp.page.waitForLoadState('networkidle', {
      timeout: 10000,
    });

    // 处理详情页面
    await this.processDetailPage();

    // 返回列表页面
    await this.navigationManager.goBack();

    return true;
  }

  /**
   * 批量处理提款申请号
   */
  async processWithdrawalNumbers(numbers) {
    if (numbers.length === 0) {
      logger.warn('没有有效的提款申请号需要处理');
      return;
    }

    logger.info(`开始批量处理 ${numbers.length} 个提款申请号`);
    const results = {
      total: numbers.length,
      success: 0,
      failed: 0,
      failedNumbers: [],
      startTime: Date.now(),
    };

    for (let i = 0; i < numbers.length; i++) {
      const number = numbers[i];
      logger.progress(i + 1, numbers.length, `查询 ${number}`);

      try {
        await this.searchSingleWithdrawal(number);
        results.success++;
      } catch (error) {
        logger.error(`查询提款申请号 ${number} 失败`, error);
        results.failed++;
        results.failedNumbers.push(number);
      }

      // 查询间隔
      if (i < numbers.length - 1) {
        await this.elementOp.page.waitForTimeout(2000);
      }
    }

    this.logProcessingResults(results);
    return results;
  }

  /**
   * 记录处理结果
   */
  logProcessingResults(results) {
    const duration = Date.now() - results.startTime;
    const avgTime = duration / results.total;

    // 设置每条记录的原处理时间为 23±6 分钟（22-28分钟）
    const manualTimePerItem = (22 + Math.random() * 6) * 60 * 1000; // 转换为毫秒
    const totalManualTime = manualTimePerItem * results.total;
    const savedTime = totalManualTime - duration;

    logger.success('所有提款申请号处理完成');
    logger.table(
      {
        总数: results.total,
        成功: results.success,
        失败: results.failed,
        总耗时: formatTime(duration),
        平均耗时: formatTime(avgTime),
        预计节省: formatTime(savedTime),
      },
      '查询结果统计',
    );

    if (results.failed > 0) {
      logger.table(
        results.failedNumbers.map((num, index) => `${index + 1}. ${num}`),
        '查询失败的申请号',
      );
    }
  }
}

module.exports = WithdrawalProcessor;
